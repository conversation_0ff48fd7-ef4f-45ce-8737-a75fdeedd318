# 设计文档

## 概述

C# 8.0 WPF计算器是一个现代化的桌面应用程序，使用MVVM架构和CommunityToolkit.Mvvm框架构建。该应用程序提供基本的算术运算功能，具有直观的图形用户界面。应用程序采用MVVM架构模式，利用C# 8.0的现代特性、Source Generators和WPF的数据绑定能力，确保代码的可维护性和可测试性。

## 架构

应用程序采用MVVM架构设计，使用CommunityToolkit.Mvvm：

```
┌─────────────────────────┐
│       View (XAML)       │  ← WPF用户界面
├─────────────────────────┤
│     ViewModel           │  ← 界面逻辑和命令 (ObservableObject)
├─────────────────────────┤
│       Model             │  ← 业务逻辑和数据 (ObservableObject)
├─────────────────────────┤
│      Services           │  ← 计算服务和验证
└─────────────────────────┘
```

### 主要组件：
- **App**: 应用程序入口点和依赖注入配置
- **MainWindow**: 主窗口视图
- **CalculatorViewModel**: 主视图模型 (使用Source Generators)
- **CalculatorService**: 核心计算服务
- **CalculatorModel**: 计算器状态模型
- **OperationType**: 操作类型枚举

## 组件和接口

### 1. App 类 (WPF应用程序)
```csharp
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        // 配置依赖注入容器
        var services = new ServiceCollection();
        ConfigureServices(services);
        
        var serviceProvider = services.BuildServiceProvider();
        
        var mainWindow = serviceProvider.GetRequiredService<MainWindow>();
        mainWindow.Show();
    }
    
    private void ConfigureServices(IServiceCollection services) { }
}
```

### 2. MainWindow (视图)
```xaml
<Window x:Class="Calculator.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!-- 计算器界面布局 -->
    <!-- 显示屏、数字按钮、操作按钮 -->
</Window>
```

### 3. CalculatorViewModel (视图模型)
```csharp
public partial class CalculatorViewModel : ObservableObject
{
    [ObservableProperty]
    private string display = "0";
    
    [ObservableProperty]
    private string currentInput = string.Empty;
    
    [RelayCommand]
    private void Number(string number) { }
    
    [RelayCommand]
    private void Operation(string operation) { }
    
    [RelayCommand]
    private void Equals() { }
    
    [RelayCommand]
    private void Clear() { }
}
```

### 4. ICalculatorService 接口和实现
```csharp
public interface ICalculatorService
{
    double Calculate(double operand1, double operand2, OperationType operation);
    bool TryParseNumber(string input, out double result);
}

public class CalculatorService : ICalculatorService
{
    // 实现计算逻辑，使用C# 8.0 switch表达式
}
```

### 5. CalculatorModel (模型)
```csharp
public partial class CalculatorModel : ObservableObject
{
    [ObservableProperty]
    private double firstOperand;
    
    [ObservableProperty]
    private double secondOperand;
    
    [ObservableProperty]
    private OperationType? currentOperation;
    
    [ObservableProperty]
    private bool isOperationPending;
    
    [ObservableProperty]
    private string? errorMessage;
}
```

### 6. OperationType 枚举
```csharp
public enum OperationType
{
    Addition,
    Subtraction,
    Multiplication,
    Division
}
```

### 7. CalculatorException 类
```csharp
public class CalculatorException : Exception
{
    public CalculatorException(string message) : base(message) { }
    public CalculatorException(string message, Exception innerException) : base(message, innerException) { }
}
```

## 数据模型

### MVVM数据流：
1. **用户交互**: 用户点击WPF界面按钮
2. **命令执行**: ViewModel中的DelegateCommand处理用户操作
3. **模型更新**: CalculatorModel状态更新
4. **服务调用**: ICalculatorService执行计算逻辑
5. **界面更新**: 通过数据绑定自动更新显示

### 数据绑定结构：
- **Display属性**: 绑定到界面显示屏，显示当前输入或结果
- **命令绑定**: 按钮Command属性绑定到ViewModel的DelegateCommand
- **错误状态**: 通过属性变更通知更新界面错误显示

### 数据类型：
- **操作数**: `double` 类型，支持整数和小数
- **操作类型**: `OperationType` 枚举
- **显示文本**: `string` 类型，用于界面显示
- **状态标志**: `bool` 类型，用于控制界面状态

## 错误处理

### WPF MVVM错误处理策略：

1. **除零错误**
   - 检测：在CalculatorService中检查除数是否为零
   - 处理：通过ViewModel的ErrorMessage属性显示错误，界面通过数据绑定显示

2. **输入验证错误**
   - 检测：在ViewModel的命令执行前验证输入
   - 处理：通过INotifyDataErrorInfo接口提供实时验证反馈

3. **数值溢出**
   - 检测：检查计算结果是否超出double范围
   - 处理：在Display属性中显示"错误"或"溢出"消息

4. **界面状态错误**
   - 检测：通过CanExecute方法控制命令可用性
   - 处理：禁用相关按钮，防止无效操作

### MVVM错误处理流程：
```
用户操作 → Command.CanExecute → [false] → 按钮禁用
         ↓ [true]
         Command.Execute → 服务调用 → [异常] → ErrorMessage更新 → 界面显示错误
                        ↓ [成功]
                        Display属性更新 → 界面自动刷新
```

## C# 8.0 特性应用

### 1. Switch 表达式
```csharp
public double Calculate(double operand1, double operand2, OperationType operation) =>
    operation switch
    {
        OperationType.Addition => operand1 + operand2,
        OperationType.Subtraction => operand1 - operand2,
        OperationType.Multiplication => operand1 * operand2,
        OperationType.Division when operand2 != 0 => operand1 / operand2,
        OperationType.Division => throw new CalculatorException("除数不能为零"),
        _ => throw new ArgumentException("无效的操作类型")
    };
```

### 2. 可空引用类型
```csharp
#nullable enable
public class CalculatorViewModel : BindableBase
{
    private string? _errorMessage;
    public string? ErrorMessage 
    { 
        get => _errorMessage; 
        set => SetProperty(ref _errorMessage, value); 
    }
}
```

### 3. 属性模式和when子句
```csharp
public bool CanExecuteOperation(string? operation) => operation switch
{
    "+" or "-" or "*" or "/" when !string.IsNullOrEmpty(Display) => true,
    _ => false
};
```

### 4. 使用声明 (Using declarations)
```csharp
// 在需要资源管理的场景中使用
using var serviceScope = container.CreateScope();
```

## 测试策略

### 单元测试覆盖：

1. **CalculatorService测试**
   - 测试所有基本运算的正确性
   - 测试除零异常处理
   - 测试边界值（最大值、最小值、零）
   - 测试小数运算精度

2. **CalculatorViewModel测试**
   - 测试命令执行逻辑
   - 测试属性变更通知
   - 测试数据绑定属性
   - 测试错误状态处理

3. **CalculatorModel测试**
   - 测试状态管理
   - 测试属性验证
   - 测试数据持久性

### WPF界面测试：
- 使用WPF测试框架测试界面交互
- 测试数据绑定的正确性
- 测试命令绑定和按钮响应

### 集成测试：
- 测试完整的MVVM数据流
- 测试Prism容器依赖注入
- 测试连续计算功能
- 测试错误恢复机制

### 测试框架：
- 使用xUnit进行单元测试
- 使用Moq进行服务模拟
- 使用Microsoft.Toolkit.Win32.UI.Controls进行WPF UI测试

## 性能考虑

- 使用`double`类型平衡精度和性能
- 避免不必要的字符串分配
- 使用StringBuilder处理大量字符串操作（如果需要）
- 输入验证采用早期返回模式提高效率

## 安全考虑

- 输入验证防止注入攻击
- 异常处理避免敏感信息泄露
- 数值范围检查防止溢出攻击